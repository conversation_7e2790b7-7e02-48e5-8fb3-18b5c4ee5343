using FluentBlue.Shared.Authorization;
using FluentBlue.WebApi.Client;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.IdentityModel.Abstractions;
using System.Globalization;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text.Json;

namespace FluentBlue.UI.Main.Auth
{
    public class JwtAuthenticationStateProvider : AuthenticationStateProvider, ILoginService
    {
        private readonly Blazored.LocalStorage.ILocalStorageService localStorage;
        private readonly HttpClient httpClient;
        private readonly string tokenKey = "Token";
        private readonly string tokenExpirationDateKey = "TokenExpirationDate";
        //private readonly string userFirstNameKey = "UserFirstName";
        //private readonly string userLastNameKey = "UserLastName";
        //private readonly string userFullNameKey = "UserFullName";
        //private readonly string userUsernameKey = "Username";
        //private readonly string userIdKey = "UserId";
        //private readonly string roleName = "RoleName";
        //private readonly string tenantIdKey = "TenantId";
        private readonly AuthenticationWebApiClient authenticationWebApiClient;
        private AuthenticationState anonymous = new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));

        public JwtAuthenticationStateProvider(HttpClient httpClient, AuthenticationWebApiClient authenticationWebApiClient, Blazored.LocalStorage.ILocalStorageService localStorage)
        {
            this.localStorage = localStorage;
            this.httpClient = httpClient;
            this.authenticationWebApiClient = authenticationWebApiClient;
        }
        public async override Task<AuthenticationState> GetAuthenticationStateAsync()
        {
            string? token = await localStorage.GetItemAsStringAsync(tokenKey);

            //Αν δεν βρήκε το token στο local storage
            if (string.IsNullOrEmpty(token))
            {
                return anonymous;
            }

            var expirationTimeString = await localStorage.GetItemAsStringAsync(tokenExpirationDateKey);
            DateTime expirationTime;

            if (DateTime.TryParse(expirationTimeString, out expirationTime))
            {
                if (IsTokenExpired(expirationTime))
                {
                    await CleanUp();
                    return anonymous;
                }

                if (ShouldRenewToken(expirationTime))
                {
                    token = await RenewToken(token);
                }
            }

            return BuildAuthenticationState(token);
        }

        public async Task TryRenewToken()
        {
            var expirationTimeString = await this.localStorage.GetItemAsStringAsync(this.tokenExpirationDateKey);
            DateTime expirationTime;

            if (DateTime.TryParse(expirationTimeString, out expirationTime))
            {
                if (IsTokenExpired(expirationTime))
                {
                    await Logout();
                }

                if (ShouldRenewToken(expirationTime))
                {
                    string? token = await this.localStorage.GetItemAsStringAsync(this.tokenKey);
                    var newToken = await RenewToken(token!);
                    var authState = BuildAuthenticationState(newToken);
                    NotifyAuthenticationStateChanged(Task.FromResult(authState));
                }
            }
        }

        private async Task<string> RenewToken(string token)
        {
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", token);
            var newToken = await this.authenticationWebApiClient.RenewToken();
            await this.localStorage.SetItemAsStringAsync(this.tokenKey, newToken.Token);
            await this.localStorage.SetItemAsStringAsync(this.tokenExpirationDateKey, newToken.Expiration.ToString());
            return newToken.Token;
        }

        private bool ShouldRenewToken(DateTime expirationTime)
        {
            return expirationTime.Subtract(DateTime.UtcNow) < TimeSpan.FromMinutes(5);
        }

        private bool IsTokenExpired(DateTime expirationTime)
        {
            return expirationTime <= DateTime.UtcNow;
        }

        public AuthenticationState BuildAuthenticationState(string token)
        {
            Dictionary<string, Claim> claimsDictionary;

            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", token);
            IEnumerable<Claim> claims = FluentBlue.Shared.Authorization.JwtHelper.ParseClaimsFromJwt(token, out claimsDictionary);
            AuthenticatedUserData.RetrieveDataFromClaims(claimsDictionary);
            

            return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity(claims, "jwt")));
        }

       

        public async Task Login(UserToken userToken)
        {
            Dictionary<string, Claim> claimsDictionary;

            await this.localStorage.SetItemAsStringAsync(this.tokenKey, userToken.Token);
            await this.localStorage.SetItemAsStringAsync(this.tokenExpirationDateKey, userToken.Expiration.ToString());
            IEnumerable<Claim> claims = FluentBlue.Shared.Authorization.JwtHelper.ParseClaimsFromJwt(userToken.Token, out claimsDictionary);

            var authState = BuildAuthenticationState(userToken.Token);
            NotifyAuthenticationStateChanged(Task.FromResult(authState));
        }

        public async Task Logout()
        {
            await CleanUp();
            NotifyAuthenticationStateChanged(Task.FromResult(anonymous));
        }

        private async Task CleanUp()
        {
            await this.localStorage.RemoveItemAsync(this.tokenKey);
            await this.localStorage.RemoveItemAsync(this.tokenExpirationDateKey);
            httpClient.DefaultRequestHeaders.Authorization = null;
        }
    }
}
